<template>
  <div class="test-components">
    <h1>ScrollView 和 AutoLoad 组件测试</h1>
    
    <div class="component-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.key"
        :class="{ active: activeTab === tab.key }"
        @click="activeTab = tab.key"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- ScrollView 测试 -->
    <div v-if="activeTab === 'scrollview'" class="test-section">
      <h2>ScrollView 组件测试</h2>
      
      <div class="test-container">
        <ScrollView
          ref="scrollViewRef"
          :scroll-y="true"
          :refresher-enabled="true"
          :refresher-triggered="isRefreshing"
          :lower-threshold="50"
          @scroll="handleScroll"
          @scrolltolower="handleScrollToLower"
          @refresherrefresh="handleRefresh"
        >
          <div v-for="item in scrollList" :key="item" class="scroll-item">
            ScrollView 列表项 {{ item }}
          </div>
        </ScrollView>
      </div>

      <div class="controls">
        <button @click="scrollToTop">滚动到顶部</button>
        <button @click="scrollToBottom">滚动到底部</button>
        <button @click="addScrollItems">添加项目</button>
      </div>
    </div>

    <!-- AutoLoad 测试 -->
    <div v-if="activeTab === 'autoload'" class="test-section">
      <h2>AutoLoad 组件测试</h2>
      
      <div class="test-container">
        <AutoLoad
          ref="autoLoadRef"
          :loading="autoLoading"
          :refreshing="autoRefreshing"
          :has-more="autoHasMore"
          :show-empty="autoList.length === 0 && !autoLoading"
          @load-more="handleAutoLoadMore"
          @refresh="handleAutoRefresh"
        >
          <div v-for="item in autoList" :key="item.id" class="auto-item">
            <h4>{{ item.title }}</h4>
            <p>{{ item.description }}</p>
            <small>{{ item.time }}</small>
          </div>
        </AutoLoad>
      </div>

      <div class="controls">
        <button @click="manualLoadMore">手动加载更多</button>
        <button @click="manualRefresh">手动刷新</button>
        <button @click="clearAutoList">清空列表</button>
      </div>
    </div>

    <!-- Hook 测试 -->
    <div v-if="activeTab === 'hook'" class="test-section">
      <h2>useAutoLoad Hook 测试</h2>
      
      <div class="test-container">
        <AutoLoad
          :loading="loading"
          :refreshing="refreshing"
          :has-more="hasMore"
          :show-empty="isEmpty"
          @load-more="loadMore"
          @refresh="refresh"
        >
          <div v-for="item in data" :key="item.id" class="hook-item">
            <div class="item-avatar">{{ item.id }}</div>
            <div class="item-content">
              <h4>{{ item.title }}</h4>
              <p>{{ item.description }}</p>
              <small>{{ item.time }}</small>
            </div>
          </div>

          <template #empty>
            <div class="empty-state">
              <div class="empty-icon">📋</div>
              <h3>暂无数据</h3>
              <p>点击刷新按钮加载数据</p>
              <button @click="refresh" class="refresh-btn">刷新数据</button>
            </div>
          </template>
        </AutoLoad>
      </div>

      <div class="controls">
        <button @click="addHookData">添加数据</button>
        <button @click="resetHookData">重置数据</button>
        <button @click="setHookData">设置测试数据</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ScrollView from '@/components/ScrollView'
import AutoLoad, { useAutoLoad } from '@/components/AutoLoad'
import type { ScrollViewInstance, AutoLoadInstance } from '@/components'

// 组件引用
const scrollViewRef = ref<ScrollViewInstance>()
const autoLoadRef = ref<AutoLoadInstance>()

// 标签页
const activeTab = ref('scrollview')
const tabs = [
  { key: 'scrollview', label: 'ScrollView' },
  { key: 'autoload', label: 'AutoLoad' },
  { key: 'hook', label: 'Hook 测试' },
]

// ScrollView 测试数据
const scrollList = ref<number[]>([])
const isRefreshing = ref(false)

// AutoLoad 测试数据
const autoList = ref<Array<{ id: number; title: string; description: string; time: string }>>([])
const autoLoading = ref(false)
const autoRefreshing = ref(false)
const autoHasMore = ref(true)
const autoPage = ref(1)

// Hook 测试
const {
  data,
  loading,
  refreshing,
  hasMore,
  isEmpty,
  loadMore,
  refresh,
  reset: resetHookData,
  setData: setHookData,
  addData,
} = useAutoLoad({
  pageSize: 10,
  immediate: false, // 不自动加载
  loadData: async (page, pageSize) => {
    // 模拟 API 请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const startId = (page - 1) * pageSize + 1
    const mockData = Array.from({ length: pageSize }, (_, i) => ({
      id: startId + i,
      title: `Hook 数据项 ${startId + i}`,
      description: `这是通过 Hook 管理的第 ${page} 页第 ${i + 1} 个数据项`,
      time: new Date().toLocaleString(),
    }))

    return {
      data: mockData,
      total: 50, // 模拟总数
    }
  },
  onError: (error) => {
    console.error('Hook 加载失败:', error)
  },
})

/**
 * 初始化 ScrollView 数据
 */
function initScrollData() {
  scrollList.value = Array.from({ length: 30 }, (_, i) => i + 1)
}

/**
 * 初始化 AutoLoad 数据
 */
function initAutoData() {
  autoList.value = Array.from({ length: 10 }, (_, i) => ({
    id: i + 1,
    title: `AutoLoad 项目 ${i + 1}`,
    description: `这是 AutoLoad 组件的第 ${i + 1} 个测试项目`,
    time: new Date().toLocaleString(),
  }))
}

/**
 * ScrollView 事件处理
 */
function handleScroll(event: any) {
  console.log('ScrollView 滚动:', event.detail)
}

function handleScrollToLower() {
  console.log('ScrollView 滚动到底部')
  // 自动添加更多项目
  const currentLength = scrollList.value.length
  const newItems = Array.from({ length: 10 }, (_, i) => currentLength + i + 1)
  scrollList.value.push(...newItems)
}

function handleRefresh() {
  console.log('ScrollView 开始刷新')
  isRefreshing.value = true
  
  setTimeout(() => {
    scrollList.value = Array.from({ length: 30 }, (_, i) => i + 1)
    isRefreshing.value = false
    scrollViewRef.value?.stopPullRefresh()
  }, 2000)
}

function scrollToTop() {
  scrollViewRef.value?.scrollTo({ top: 0, animated: true })
}

function scrollToBottom() {
  const scrollInfo = scrollViewRef.value?.getScrollInfo()
  if (scrollInfo) {
    scrollViewRef.value?.scrollTo({
      top: scrollInfo.scrollHeight - scrollInfo.clientHeight,
      animated: true,
    })
  }
}

function addScrollItems() {
  const currentLength = scrollList.value.length
  const newItems = Array.from({ length: 5 }, (_, i) => currentLength + i + 1)
  scrollList.value.push(...newItems)
}

/**
 * AutoLoad 事件处理
 */
async function handleAutoLoadMore() {
  if (autoLoading.value || !autoHasMore.value) return
  
  autoLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const startId = autoPage.value * 10 + 1
    const newData = Array.from({ length: 10 }, (_, i) => ({
      id: startId + i,
      title: `AutoLoad 项目 ${startId + i}`,
      description: `这是第 ${autoPage.value + 1} 页的第 ${i + 1} 个项目`,
      time: new Date().toLocaleString(),
    }))
    
    autoList.value.push(...newData)
    autoPage.value++
    
    if (autoPage.value >= 5) {
      autoHasMore.value = false
    }
  } finally {
    autoLoading.value = false
  }
}

async function handleAutoRefresh() {
  if (autoRefreshing.value) return
  
  autoRefreshing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const newData = Array.from({ length: 10 }, (_, i) => ({
      id: i + 1,
      title: `AutoLoad 项目 ${i + 1}`,
      description: `这是刷新后的第 ${i + 1} 个项目`,
      time: new Date().toLocaleString(),
    }))
    
    autoList.value = newData
    autoPage.value = 1
    autoHasMore.value = true
  } finally {
    autoRefreshing.value = false
  }
}

function manualLoadMore() {
  autoLoadRef.value?.loadMore()
}

function manualRefresh() {
  autoLoadRef.value?.refresh()
}

function clearAutoList() {
  autoList.value = []
  autoPage.value = 1
  autoHasMore.value = true
}

/**
 * Hook 测试方法
 */
function addHookData() {
  const newItem = {
    id: Date.now(),
    title: `手动添加项目 ${Date.now()}`,
    description: `这是手动添加的项目，时间：${new Date().toLocaleString()}`,
    time: new Date().toLocaleString(),
  }
  addData(newItem)
}

function setTestData() {
  const testData = Array.from({ length: 5 }, (_, i) => ({
    id: i + 1,
    title: `测试数据 ${i + 1}`,
    description: `这是设置的测试数据第 ${i + 1} 项`,
    time: new Date().toLocaleString(),
  }))
  setHookData(testData)
}

// 初始化数据
initScrollData()
initAutoData()
</script>

<style lang="scss" scoped>
.test-components {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }

  .component-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    gap: 10px;

    button {
      padding: 10px 20px;
      border: 1px solid #ddd;
      background: #fff;
      color: #666;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        border-color: #409eff;
        color: #409eff;
      }

      &.active {
        background: #409eff;
        border-color: #409eff;
        color: white;
      }
    }
  }

  .test-section {
    h2 {
      margin-bottom: 20px;
      color: #333;
      text-align: center;
    }
  }

  .test-container {
    height: 500px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
  }

  .scroll-item,
  .auto-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
    }

    h4 {
      margin: 0 0 8px 0;
      color: #333;
    }

    p {
      margin: 0 0 8px 0;
      color: #666;
      font-size: 14px;
    }

    small {
      color: #999;
      font-size: 12px;
    }
  }

  .hook-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
    }

    .item-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(45deg, #409eff, #66b1ff);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 15px;
      font-size: 14px;
    }

    .item-content {
      flex: 1;

      h4 {
        margin: 0 0 5px 0;
        color: #333;
        font-size: 16px;
      }

      p {
        margin: 0 0 5px 0;
        color: #666;
        font-size: 14px;
      }

      small {
        color: #999;
        font-size: 12px;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    h3 {
      margin: 0 0 8px 0;
      color: #333;
    }

    p {
      margin: 0 0 20px 0;
      color: #666;
    }

    .refresh-btn {
      padding: 8px 16px;
      border: 1px solid #409eff;
      background: #409eff;
      color: white;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #66b1ff;
      }
    }
  }

  .controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;

    button {
      padding: 10px 20px;
      border: 1px solid #409eff;
      background: #409eff;
      color: white;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #66b1ff;
        border-color: #66b1ff;
      }

      &:active {
        background: #3a8ee6;
        border-color: #3a8ee6;
      }
    }
  }
}
</style>
