/**
 * AutoLoad 数据管理 Hook
 * 提供列表数据的自动加载、刷新、分页等功能
 */

import { ref, computed, onMounted, type Ref } from "vue";
import type { UseAutoLoadOptions, UseAutoLoadReturn } from "./types";

/**
 * 使用 AutoLoad 数据管理
 * @param options 配置选项
 * @returns 数据管理对象
 */
export function useAutoLoad<T = any>(
  options: UseAutoLoadOptions<T>
): UseAutoLoadReturn<T> {
  const {
    initialData = [],
    pageSize = 20,
    loadData,
    immediate = true,
    onError,
    onSuccess,
  } = options;

  // 状态管理
  const data = ref<T[]>([...initialData]);
  const loading = ref(false);
  const refreshing = ref(false);
  const currentPage = ref(1);
  const total = ref(0);
  const error = ref<string | null>(null);
  const hasLoadedFirstPage = ref(false);

  /**
   * 计算属性
   */
  const hasMore = computed(() => {
    if (!hasLoadedFirstPage.value) return true;
    if (total.value > 0) {
      return data.value.length < total.value;
    }
    // 如果没有总数，根据最后一次加载的数据量判断
    return data.value.length % pageSize === 0 && data.value.length > 0;
  });

  const isEmpty = computed(() => {
    return (
      !loading.value &&
      !refreshing.value &&
      data.value.length === 0 &&
      hasLoadedFirstPage.value
    );
  });

  /**
   * 加载数据
   * @param page 页码
   * @param isRefresh 是否为刷新操作
   */
  async function fetchData(page: number, isRefresh = false): Promise<void> {
    try {
      error.value = null;

      if (isRefresh) {
        refreshing.value = true;
      } else {
        loading.value = true;
      }

      const result = await loadData(page, pageSize);

      if (isRefresh) {
        // 刷新时替换所有数据
        data.value = [...result.data];
        currentPage.value = 1;
      } else {
        // 加载更多时追加数据
        if (page === 1) {
          data.value = [...result.data];
        } else {
          data.value = [...data.value, ...result.data];
        }
        currentPage.value = page;
      }

      // 更新总数
      if (typeof result.total === "number") {
        total.value = result.total;
      }

      hasLoadedFirstPage.value = true;

      // 成功回调
      onSuccess?.(result.data, isRefresh);
    } catch (err: any) {
      error.value = err.message || "加载失败";
      onError?.(err);
      throw err;
    } finally {
      loading.value = false;
      refreshing.value = false;
    }
  }

  /**
   * 加载更多数据
   */
  async function loadMore(): Promise<void> {
    if (loading.value || refreshing.value || !hasMore.value) {
      return;
    }

    const nextPage = currentPage.value + 1;
    await fetchData(nextPage, false);
  }

  /**
   * 刷新数据
   */
  async function refresh(): Promise<void> {
    if (loading.value || refreshing.value) {
      return;
    }

    await fetchData(1, true);
  }

  /**
   * 重置数据
   */
  function reset(): void {
    data.value = [...initialData];
    loading.value = false;
    refreshing.value = false;
    currentPage.value = 1;
    total.value = 0;
    error.value = null;
    hasLoadedFirstPage.value = false;
  }

  /**
   * 手动设置数据
   * @param newData 新数据
   */
  function setData(newData: T[]): void {
    data.value = [...newData];
    hasLoadedFirstPage.value = true;
  }

  /**
   * 添加数据
   * @param newData 要添加的数据
   */
  function addData(newData: T | T[]): void {
    if (Array.isArray(newData)) {
      data.value = [...data.value, ...newData];
    } else {
      data.value = [...data.value, newData];
    }
  }

  /**
   * 移除数据
   * @param predicate 判断函数
   */
  function removeData(predicate: (item: T, index: number) => boolean): void {
    data.value = data.value.filter((item, index) => !predicate(item, index));
  }

  /**
   * 更新数据
   * @param predicate 判断函数
   * @param updater 更新函数
   */
  function updateData(
    predicate: (item: T, index: number) => boolean,
    updater: (item: T) => T
  ): void {
    data.value = data.value.map((item, index) => {
      if (predicate(item, index)) {
        return updater(item);
      }
      return item;
    });
  }

  // 自动加载第一页数据
  onMounted(() => {
    if (immediate && initialData.length === 0) {
      fetchData(1, false).catch(() => {
        // 错误已在 fetchData 中处理
      });
    }
  });

  return {
    data,
    loading,
    refreshing,
    hasMore,
    currentPage,
    total,
    isEmpty,
    error,
    loadMore,
    refresh,
    reset,
    setData,
    addData,
    removeData,
    updateData,
  };
}
