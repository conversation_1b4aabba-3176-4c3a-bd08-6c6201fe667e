import { ReactNode } from 'react';
import { Loader2 } from 'lucide-react';
import { usePullToLoadMore } from '@/hooks/usePullToLoadMore';

interface PullToLoadMoreProps {
  /** 子组件 */
  children: ReactNode;
  /** 是否还有更多数据 */
  hasMore: boolean;
  /** 是否正在加载 */
  loading: boolean;
  /** 加载更多的回调函数 */
  onLoadMore: () => void;
  /** 触发加载的距离阈值，默认100px */
  threshold?: number;
  /** 节流延迟时间，默认200ms */
  throttleDelay?: number;
  /** 是否启用，默认true */
  enabled?: boolean;
  /** 加载中的提示文本 */
  loadingText?: string;
  /** 没有更多数据的提示文本 */
  noMoreText?: string;
  /** 自定义加载中的渲染 */
  renderLoading?: () => ReactNode;
  /** 自定义没有更多数据的渲染 */
  renderNoMore?: () => ReactNode;
}

/**
 * 上拉加载更多组件
 * 包装子组件并提供上拉加载功能
 */
export function PullToLoadMore({
  children,
  hasMore,
  loading,
  onLoadMore,
  threshold = 100,
  throttleDelay = 200,
  enabled = true,
  loadingText = '加载中...',
  noMoreText = '没有更多数据了',
  renderLoading,
  renderNoMore,
}: PullToLoadMoreProps) {
  // 使用上拉加载Hook
  usePullToLoadMore({
    threshold,
    hasMore,
    loading,
    onLoadMore,
    throttleDelay,
    enabled,
  });

  /**
   * 渲染加载状态
   */
  const renderLoadingState = () => {
    if (renderLoading) {
      return renderLoading();
    }

    return (
      <div className="flex items-center justify-center py-4">
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <span className="text-sm text-muted-foreground">{loadingText}</span>
      </div>
    );
  };

  /**
   * 渲染没有更多数据状态
   */
  const renderNoMoreState = () => {
    if (renderNoMore) {
      return renderNoMore();
    }

    return (
      <div className="text-center py-4">
        <p className="text-sm text-muted-foreground">{noMoreText}</p>
      </div>
    );
  };

  return (
    <div>
      {children}
      
      {/* 加载状态指示器 */}
      {enabled && loading && renderLoadingState()}
      
      {/* 没有更多数据提示 */}
      {enabled && !hasMore && !loading && renderNoMoreState()}
    </div>
  );
}