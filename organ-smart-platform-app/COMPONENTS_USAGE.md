# ScrollView 和 AutoLoad 组件使用指南

## 🚀 快速开始

### 1. 基础使用

#### ScrollView 组件
```vue
<template>
  <ScrollView :scroll-y="true" style="height: 400px;">
    <div v-for="item in list" :key="item.id">
      {{ item.name }}
    </div>
  </ScrollView>
</template>

<script setup lang="ts">
import ScrollView from '@/components/ScrollView'
</script>
```

#### AutoLoad 组件
```vue
<template>
  <AutoLoad
    :loading="loading"
    :has-more="hasMore"
    @load-more="loadMore"
    style="height: 400px;"
  >
    <div v-for="item in list" :key="item.id">
      {{ item.name }}
    </div>
  </AutoLoad>
</template>

<script setup lang="ts">
import AutoLoad from '@/components/AutoLoad'
import { ref } from 'vue'

const list = ref([])
const loading = ref(false)
const hasMore = ref(true)

async function loadMore() {
  loading.value = true
  try {
    const newData = await fetchData()
    list.value.push(...newData)
  } finally {
    loading.value = false
  }
}
</script>
```

### 2. 使用 Hook 简化开发

```vue
<template>
  <AutoLoad
    :loading="loading"
    :refreshing="refreshing"
    :has-more="hasMore"
    :show-empty="isEmpty"
    @load-more="loadMore"
    @refresh="refresh"
  >
    <div v-for="item in data" :key="item.id">
      {{ item.name }}
    </div>
  </AutoLoad>
</template>

<script setup lang="ts">
import AutoLoad, { useAutoLoad } from '@/components/AutoLoad'

const {
  data,
  loading,
  refreshing,
  hasMore,
  isEmpty,
  loadMore,
  refresh,
} = useAutoLoad({
  pageSize: 20,
  loadData: async (page, pageSize) => {
    const response = await api.getList({ page, pageSize })
    return {
      data: response.data,
      total: response.total,
    }
  },
})
</script>
```

## 🔧 组件特性

### ScrollView
- ✅ 支持横向和纵向滚动
- ✅ 支持下拉刷新
- ✅ 支持滚动到指定位置
- ✅ 支持自定义滚动阈值
- ✅ 支持滚动动画

### AutoLoad
- ✅ 自动管理加载状态
- ✅ 支持下拉刷新和上拉加载
- ✅ 支持空状态和错误状态
- ✅ 提供数据管理 Hook
- ✅ 支持自定义样式

## 📁 文件位置

```
src/components/
├── ScrollView/
│   ├── index.vue          # 组件主文件
│   ├── types.ts           # TypeScript 类型
│   ├── index.ts           # 导出文件
│   └── README.md          # 详细文档
├── AutoLoad/
│   ├── index.vue          # 组件主文件
│   ├── types.ts           # TypeScript 类型
│   ├── useAutoLoad.ts     # Hook 实现
│   ├── index.ts           # 导出文件
│   └── README.md          # 详细文档
└── demo.vue               # 演示页面
```

## 🧪 测试页面

项目中提供了多个测试页面：

1. **demo.vue** - 基础演示页面
2. **test-simple.vue** - 简单测试页面
3. **test-components.vue** - 完整功能测试

可以在路由中添加这些页面进行测试：

```typescript
// router/index.ts
{
  path: '/demo',
  component: () => import('@/components/demo.vue')
}
```

## ⚠️ 注意事项

1. **Vue 版本**: 组件基于 Vue 2.7 + Composition API 开发
2. **TypeScript**: 提供完整的类型支持
3. **样式**: 使用 SCSS，与 Element UI 风格保持一致
4. **性能**: 内置节流和防抖机制，适合大列表场景

## 🐛 常见问题

### Q: 组件导入失败？
A: 确保使用正确的导入路径：
```typescript
import ScrollView from '@/components/ScrollView'
import AutoLoad from '@/components/AutoLoad'
```

### Q: useAutoLoad Hook 类型错误？
A: 确保 loadData 函数返回正确的数据格式：
```typescript
loadData: async (page, pageSize) => {
  return {
    data: [], // 必须
    total: 0, // 可选，用于判断是否还有更多数据
  }
}
```

### Q: 下拉刷新不工作？
A: 确保：
1. 设置了 `refresher-enabled="true"`
2. 监听了 `@refresherrefresh` 事件
3. 刷新完成后调用 `stopPullRefresh()` 方法

## 📚 更多文档

- [ScrollView 详细文档](./src/components/ScrollView/README.md)
- [AutoLoad 详细文档](./src/components/AutoLoad/README.md)
- [组件库总览](./src/components/README.md)
